# IonAlumni - Job and Event Management System

## Project Overview

## The EVENT Section/Login is for Samarth Meti
## The JOB Section/Login is for <PERSON><PERSON>

This document provides a comprehensive overview of the JobController and EventController implementations, along with their related files and routing configurations for the IonAlumni platform.

## Table of Contents

1. [JobController Implementation](#jobcontroller-implementation)
2. [EventController Implementation](#eventcontroller-implementation)
3. [Route Configurations](#route-configurations)
4. [Validation Middleware](#validation-middleware)
5. [Socket Integration](#socket-integration)
6. [Database Models](#database-models)
7. [Project Structure](#project-structure)

## This is for <PERSON><PERSON>

## JobController Implementation

### File: `API/src/controllers/jobController.ts`

```typescript
import { Request, Response, NextFunction } from "express";
import { JobType, WorkMode } from "@prisma/client";
import { prisma } from "../config/database";
import { createError } from "../middleware/errorHandler";
import { SocketUtils } from "../utils/socketUtils";
import { CacheService } from "../services/cacheService";
import { CacheKeys } from "../config/cache";
import { Logger } from "../services/loggerService";

interface CreateJobRequest {
  title: string;
  company_name: string;
  location?: string;
  description: string;
  apply_link_or_email: string;
  job_type?: JobType;
  work_mode: WorkMode;
  experience_level?: string;
  is_public?: boolean;
}

interface UpdateJobRequest extends Partial<CreateJobRequest> {}

interface JobFilters {
  job_type?: JobType;
  work_mode?: WorkMode;
  location?: string;
  company_name?: string;
  experience_level?: string;
  search?: string;
}

/**
 * Create a new job posting
 */
export const createJob = async (req: Request<{}, {}, CreateJobRequest>, res: Response, next: NextFunction) => {
  try {
    //*TODO: Implement createJob
  } catch (error) {
    next(error);
  }
};

/**
 * Get all jobs with filtering and pagination
 */
export const getJobs = async (req: Request, res: Response, next: NextFunction) => {
  try {
    //*TODO: Implement getJobs
  } catch (error) {
    next(error);
  }
};

/**
 * Get job by ID
 */
export const getJobById = async (req: Request, res: Response, next: NextFunction) => {
  try {
    //*TODO: Implement getJobById
  } catch (error) {
    next(error);
  }
};

/**
 * Update job by ID
 */
export const updateJob = async (req: Request<{ id: string }, {}, UpdateJobRequest>, res: Response, next: NextFunction) => {
  try {
    //*TODO: Implement updateJob
  } catch (error) {
    next(error);
  }
};

/**
 * Delete job by ID
 */
export const deleteJob = async (req: Request, res: Response, next: NextFunction) => {
  try {
    //*TODO: Implement deleteJob
  } catch (error) {
    next(error);
  }
};

/**
 * Get jobs posted by current user
 */
export const getMyJobs = async (req: Request, res: Response, next: NextFunction) => {
  try {
    //*TODO: Implement getMyJobs
  } catch (error) {
    next(error);
  }
};
```
## This is for Samarth Meti

## JobController Implementation

### File: `API/src/controllers/jobController.ts`

```typescript
import { Request, Response, NextFunction } from "express";
import { prisma } from "../config/database";
import { createError } from "../middleware/errorHandler";
import { SocketUtils } from "../utils/socketUtils";
import { CacheService } from "../services/cacheService";
import { CacheKeys } from "../config/cache";
import { Logger } from "../services/loggerService";

interface CreateEventRequest {
  title: string;
  description?: string;
  image_url?: string;
  rsvp_link?: string;
  start_time: string;
  end_time?: string;
  location?: string;
  is_public?: boolean;
}

interface UpdateEventRequest extends Partial<CreateEventRequest> {}

interface EventFilters {
  location?: string;
  search?: string;
  start_date?: string;
  end_date?: string;
}

THIS IS FOR SAMARTH METI
/**
 * Create a new event
 */
export const createEvent = async (req: Request<{}, {}, CreateEventRequest>, res: Response, next: NextFunction) => {
  //*TODO: Implement createEvent
  } catch (error) {
    next(error);
  }
};

/**
 * Get all events with filtering and pagination
 */
export const getEvents = async (req: Request, res: Response, next: NextFunction) => {
  try {
    //*TODO: Implement getEvents
  } catch (error) {
    next(error);
  }
};

/**
 * Get event by ID
 */
export const getEventById = async (req: Request, res: Response, next: NextFunction) => {
  try {
    //*TODO: Implement getEventById
  } catch (error) {
    next(error);
  }
};

/**
 * Update event by ID
 */
export const updateEvent = async (req: Request<{ id: string }, {}, UpdateEventRequest>, res: Response, next: NextFunction) => {
  try {
    //*TODO: Implement updateEvent
  } catch (error) {
    next(error);
  }
};

/**
 * Delete event by ID
 */
export const deleteEvent = async (req: Request, res: Response, next: NextFunction) => {
  try {
    //*TODO: Implement deleteEvent
  } catch (error) {
    next(error);
  }
};

/**
 * Get events created by current user
 */
export const getMyEvents = async (req: Request, res: Response, next: NextFunction) => {
  try {
    //*TODO: Implement getMyEvents
  } catch (error) {
    next(error);
  }
};
```

## This is for Sanjay Kulkarni
## JobRouter Implementation
## Route Configurations

### File: `API/src/routes/job.ts`

```typescript
import { Router } from "express";
import * as jobController from "../controllers/jobController";
import { authenticate, requireApproved } from "../middleware/auth";
import {
  createJobValidation,
  updateJobValidation,
  idValidation,
  paginationValidation,
} from "../middleware/validation";

const router = Router();

// All routes require authentication
router.use(authenticate);

// Create a new job
router.post("/", requireApproved, createJobValidation, jobController.createJob);

// Get all jobs with filtering and pagination
router.get("/", requireApproved, paginationValidation, jobController.getJobs);

// Get jobs posted by current user
router.get("/my-jobs", requireApproved, paginationValidation, jobController.getMyJobs);

// Get job by ID
router.get("/:id", requireApproved, idValidation, jobController.getJobById);

// Update job by ID
router.put("/:id", requireApproved, idValidation, updateJobValidation, jobController.updateJob);

// Delete job by ID
router.delete("/:id", requireApproved, idValidation, jobController.deleteJob);

export default router;
```

## This is for Samarth Meti

## EventRouter Implementation

### File: `API/src/routes/event.ts`

```typescript
import { Router } from "express";
import * as eventController from "../controllers/eventController";
import { authenticate, requireApproved } from "../middleware/auth";
import {
  createEventValidation,
  updateEventValidation,
  idValidation,
  paginationValidation,
} from "../middleware/validation";

const router = Router();

// All routes require authentication
router.use(authenticate);

// Create a new event
router.post("/", requireApproved, createEventValidation, eventController.createEvent);

// Get all events with filtering and pagination
router.get("/", requireApproved, paginationValidation, eventController.getEvents);

// Get events created by current user
router.get("/my-events", requireApproved, paginationValidation, eventController.getMyEvents);

// Get event by ID
router.get("/:id", requireApproved, idValidation, eventController.getEventById);

// Update event by ID
router.put("/:id", requireApproved, idValidation, updateEventValidation, eventController.updateEvent);

// Delete event by ID
router.delete("/:id", requireApproved, idValidation, eventController.deleteEvent);

export default router;
```

## Validation Middleware

### Additional validation rules for `API/src/middleware/validation.ts`

```typescript
// Job validation rules
export const createJobValidation = [
  body("title")
    .trim()
    .isLength({ min: 5, max: 255 })
    .withMessage("Title must be between 5 and 255 characters"),
  body("company_name")
    .trim()
    .isLength({ min: 2, max: 255 })
    .withMessage("Company name must be between 2 and 255 characters"),
  body("location")
    .optional()
    .trim()
    .isLength({ max: 255 })
    .withMessage("Location must not exceed 255 characters"),
  body("description")
    .trim()
    .isLength({ min: 10, max: 5000 })
    .withMessage("Description must be between 10 and 5000 characters"),
  body("apply_link_or_email")
    .trim()
    .isLength({ min: 5, max: 255 })
    .withMessage("Apply link or email must be between 5 and 255 characters")
    .custom((value) => {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      const urlRegex = /^https?:\/\/.+/;
      if (!emailRegex.test(value) && !urlRegex.test(value)) {
        throw new Error("Must be a valid email address or URL");
      }
      return true;
    }),
  body("job_type")
    .optional()
    .isIn(["FULL_TIME", "PART_TIME", "INTERNSHIP", "CONTRACT"])
    .withMessage("Job type must be FULL_TIME, PART_TIME, INTERNSHIP, or CONTRACT"),
  body("work_mode")
    .isIn(["Remote", "Hybrid", "Onsite"])
    .withMessage("Work mode must be Remote, Hybrid, or Onsite"),
  body("experience_level")
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage("Experience level must not exceed 50 characters"),
  body("is_public")
    .optional()
    .isBoolean()
    .withMessage("is_public must be a boolean"),
  handleValidationErrors,
];

export const updateJobValidation = [
  body("title")
    .optional()
    .trim()
    .isLength({ min: 5, max: 255 })
    .withMessage("Title must be between 5 and 255 characters"),
  body("company_name")
    .optional()
    .trim()
    .isLength({ min: 2, max: 255 })
    .withMessage("Company name must be between 2 and 255 characters"),
  body("location")
    .optional()
    .trim()
    .isLength({ max: 255 })
    .withMessage("Location must not exceed 255 characters"),
  body("description")
    .optional()
    .trim()
    .isLength({ min: 10, max: 5000 })
    .withMessage("Description must be between 10 and 5000 characters"),
  body("apply_link_or_email")
    .optional()
    .trim()
    .isLength({ min: 5, max: 255 })
    .withMessage("Apply link or email must be between 5 and 255 characters")
    .custom((value) => {
      if (value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        const urlRegex = /^https?:\/\/.+/;
        if (!emailRegex.test(value) && !urlRegex.test(value)) {
          throw new Error("Must be a valid email address or URL");
        }
      }
      return true;
    }),
  body("job_type")
    .optional()
    .isIn(["FULL_TIME", "PART_TIME", "INTERNSHIP", "CONTRACT"])
    .withMessage("Job type must be FULL_TIME, PART_TIME, INTERNSHIP, or CONTRACT"),
  body("work_mode")
    .optional()
    .isIn(["Remote", "Hybrid", "Onsite"])
    .withMessage("Work mode must be Remote, Hybrid, or Onsite"),
  body("experience_level")
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage("Experience level must not exceed 50 characters"),
  body("is_public")
    .optional()
    .isBoolean()
    .withMessage("is_public must be a boolean"),
  handleValidationErrors,
];

// Event validation rules
export const createEventValidation = [
  body("title")
    .trim()
    .isLength({ min: 5, max: 255 })
    .withMessage("Title must be between 5 and 255 characters"),
  body("description")
    .optional()
    .trim()
    .isLength({ max: 5000 })
    .withMessage("Description must not exceed 5000 characters"),
  body("image_url")
    .optional()
    .isURL()
    .withMessage("Image URL must be a valid URL"),
  body("rsvp_link")
    .optional()
    .isURL()
    .withMessage("RSVP link must be a valid URL"),
  body("start_time")
    .isISO8601()
    .withMessage("Start time must be a valid ISO 8601 date")
    .custom((value) => {
      const startDate = new Date(value);
      const now = new Date();
      if (startDate <= now) {
        throw new Error("Start time must be in the future");
      }
      return true;
    }),
  body("end_time")
    .optional()
    .isISO8601()
    .withMessage("End time must be a valid ISO 8601 date")
    .custom((value, { req }) => {
      if (value) {
        const endDate = new Date(value);
        const startDate = new Date(req.body.start_time);
        if (endDate <= startDate) {
          throw new Error("End time must be after start time");
        }
      }
      return true;
    }),
  body("location")
    .optional()
    .trim()
    .isLength({ max: 255 })
    .withMessage("Location must not exceed 255 characters"),
  body("is_public")
    .optional()
    .isBoolean()
    .withMessage("is_public must be a boolean"),
  handleValidationErrors,
];

export const updateEventValidation = [
  body("title")
    .optional()
    .trim()
    .isLength({ min: 5, max: 255 })
    .withMessage("Title must be between 5 and 255 characters"),
  body("description")
    .optional()
    .trim()
    .isLength({ max: 5000 })
    .withMessage("Description must not exceed 5000 characters"),
  body("image_url")
    .optional()
    .isURL()
    .withMessage("Image URL must be a valid URL"),
  body("rsvp_link")
    .optional()
    .isURL()
    .withMessage("RSVP link must be a valid URL"),
  body("start_time")
    .optional()
    .isISO8601()
    .withMessage("Start time must be a valid ISO 8601 date"),
  body("end_time")
    .optional()
    .isISO8601()
    .withMessage("End time must be a valid ISO 8601 date"),
  body("location")
    .optional()
    .trim()
    .isLength({ max: 255 })
    .withMessage("Location must not exceed 255 characters"),
  body("is_public")
    .optional()
    .isBoolean()
    .withMessage("is_public must be a boolean"),
  handleValidationErrors,
];
```

## Socket Integration

The system includes real-time socket integration for both jobs and events. The socket utilities are already implemented in the existing codebase:

### Socket Events for Jobs:
- `JOB_POSTED`: Triggered when a new job is created
- `JOB_UPDATED`: Triggered when a job is updated
- `JOB_DELETED`: Triggered when a job is deleted

### Socket Events for Events:
- `EVENT_CREATED`: Triggered when a new event is created
- `EVENT_UPDATED`: Triggered when an event is updated
- `EVENT_DELETED`: Triggered when an event is deleted

### Socket Rooms:
- `TENANT`: Tenant-specific room for broadcasting updates
- `JOB`: Job-specific room for job-related updates
- `EVENT`: Event-specific room for event-related updates

## Database Models

### Job Model (from Prisma Schema):

```prisma
model Job {
  id                  Int      @id @default(autoincrement())
  //*TODO: Implement Job Model
}

enum JobType {
  FULL_TIME
  PART_TIME
  INTERNSHIP
  CONTRACT
}

enum WorkMode {
  Remote
  Hybrid
  Onsite
}
```

### Event Model (from Prisma Schema):

```prisma
model Event {
  id          Int       @id @default(autoincrement())
  //*TODO: Implement Event Model
}
```

## Project Structure

To integrate the JobController and EventController into the existing project, you need to:

1. **Create the controller files:**
   - `API/src/controllers/jobController.ts`
   - `API/src/controllers/eventController.ts`

2. **Create the route files:**
   - `API/src/routes/job.ts`
   - `API/src/routes/event.ts`

3. **Update the main app file:**

### File: `API/src/app.ts` (Add these imports and routes)

```typescript
// Add these imports at the top
import jobRoutes from "./routes/job";
import eventRoutes from "./routes/event";

// Add these routes after the existing routes (around line 130)
app.use("/api/jobs", jobRoutes);
app.use("/api/events", eventRoutes);
```

4. **Update validation middleware:**
   - Add the job and event validation rules to `API/src/middleware/validation.ts`

## API Endpoints

### Job Endpoints:
- `POST /api/jobs` - Create a new job
- `GET /api/jobs` - Get all jobs with filtering and pagination
- `GET /api/jobs/my-jobs` - Get jobs posted by current user
- `GET /api/jobs/:id` - Get job by ID
- `PUT /api/jobs/:id` - Update job by ID
- `DELETE /api/jobs/:id` - Delete job by ID

### Event Endpoints:
- `POST /api/events` - Create a new event
- `GET /api/events` - Get all events with filtering and pagination
- `GET /api/events/my-events` - Get events created by current user
- `GET /api/events/:id` - Get event by ID
- `PUT /api/events/:id` - Update event by ID
- `DELETE /api/events/:id` - Delete event by ID

## Features

### Job Management:
- ✅ Create, read, update, delete jobs
- ✅ Job filtering by type, work mode, location, company
- ✅ Search functionality across title, description, and company
- ✅ Pagination support
- ✅ Real-time notifications via Socket.IO
- ✅ Caching for improved performance
- ✅ Permission-based access control
- ✅ Input validation and sanitization

### Event Management:
- ✅ Create, read, update, delete events
- ✅ Event filtering by location and date range
- ✅ Search functionality across title and description
- ✅ Pagination support
- ✅ Real-time notifications via Socket.IO
- ✅ Caching for improved performance
- ✅ Permission-based access control
- ✅ Input validation and sanitization
- ✅ Date validation (start/end time logic)

### Security Features:
- ✅ JWT-based authentication
- ✅ Role-based authorization
- ✅ Tenant isolation
- ✅ Input validation and sanitization
- ✅ Rate limiting
- ✅ CORS protection
- ✅ Helmet security headers

### Performance Features:
- ✅ Redis caching
- ✅ Database query optimization
- ✅ Pagination for large datasets
- ✅ Efficient database indexes
- ✅ Connection pooling

## Installation and Setup

1. **Install dependencies:**
   ```bash
   cd API
   npm install
   ```

2. **Set up environment variables:**
   ```bash
   # Database
   DATABASE_URL="mysql://username:password@localhost:3306/ionalumni"

   # JWT
   JWT_SECRET="your-jwt-secret"
   JWT_EXPIRES_IN="24h"
   JWT_REFRESH_SECRET="your-refresh-secret"
   JWT_REFRESH_EXPIRES_IN="7d"

   # Redis (for caching)
   REDIS_URL="redis://localhost:6379"

   # Email (optional)
   SMTP_HOST="smtp.gmail.com"
   SMTP_PORT=587
   SMTP_USER="<EMAIL>"
   SMTP_PASS="your-app-password"
   FROM_EMAIL="<EMAIL>"
   ```

3. **Run database migrations:**
   ```bash
   npx prisma migrate dev
   ```

4. **Start the server:**
   ```bash
   npm run dev
   ```

## Testing

The controllers include comprehensive error handling and logging. You can test the endpoints using tools like Postman or curl:

```bash
# Create a job
curl -X POST http://localhost:5000/api/jobs \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "title": "Senior Software Engineer",
    "company_name": "Tech Corp",
    "description": "We are looking for a senior software engineer...",
    "apply_link_or_email": "<EMAIL>",
    "work_mode": "Remote",
    "job_type": "FULL_TIME"
  }'

# Create an event
curl -X POST http://localhost:5000/api/events \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "title": "Alumni Networking Event",
    "description": "Join us for an evening of networking...",
    "start_time": "2024-12-01T18:00:00Z",
    "end_time": "2024-12-01T21:00:00Z",
    "location": "Tech Hub, Downtown"
  }'
```

This implementation provides a robust, scalable, and secure job and event management system that integrates seamlessly with the existing IonAlumni platform architecture.
```env
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>
```
